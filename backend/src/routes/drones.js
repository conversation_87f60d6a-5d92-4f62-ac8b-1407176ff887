const express = require('express');
const { sql } = require('../config/database');
const { authenticate, authorize, optionalAuth } = require('../middleware/auth');
const { validate, droneSchemas, uuidSchema } = require('../middleware/validation');

const router = express.Router();

// Fonction utilitaire pour créer un slug unique
const createSlug = (text) => {
  return text
    .toLowerCase()
    .replace(/[^a-z0-9 -]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim('-');
};

// @route   GET /api/drones
// @desc    Obtenir tous les drones avec filtres et pagination
// @access  Public
router.get('/', optionalAuth, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 12,
      search = '',
      category = '',
      brand = '',
      minPrice = 0,
      maxPrice = 999999,
      sortBy = 'created_at',
      sortOrder = 'DESC',
      inStock = ''
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);

    // Construire la requête avec filtres dynamiques
    let drones;
    let countResult;

    // Requête de base sans filtres
    if (!search && !category && !brand && minPrice <= 0 && maxPrice >= 999999 && inStock !== 'true') {
      // Cas simple sans filtres
      drones = await sql`
        SELECT
          d.id,
          d.name,
          d.slug,
          d.description,
          d.brand,
          d.model,
          d.price,
          d.images,
          d.specifications,
          d.features,
          d.stock_quantity,
          d.rating,
          d.review_count,
          d.weight,
          d.dimensions,
          d.battery_life,
          d.max_speed,
          d.max_range,
          d.camera_specs,
          d.created_at,
          c.id as category_id,
          c.name as category_name,
          c.slug as category_slug
        FROM drones d
        LEFT JOIN categories c ON d.category_id = c.id
        WHERE d.is_active = true
        ORDER BY d.created_at DESC
        LIMIT ${parseInt(limit)} OFFSET ${offset}
      `;

      countResult = await sql`
        SELECT COUNT(*) as total
        FROM drones d
        WHERE d.is_active = true
      `;
    } else {
      // Cas avec filtres - pour simplifier, on fait une requête basique avec les filtres principaux
      if (search) {
        drones = await sql`
          SELECT
            d.id,
            d.name,
            d.slug,
            d.description,
            d.brand,
            d.model,
            d.price,
            d.images,
            d.specifications,
            d.features,
            d.stock_quantity,
            d.rating,
            d.review_count,
            d.weight,
            d.dimensions,
            d.battery_life,
            d.max_speed,
            d.max_range,
            d.camera_specs,
            d.created_at,
            c.id as category_id,
            c.name as category_name,
            c.slug as category_slug
          FROM drones d
          LEFT JOIN categories c ON d.category_id = c.id
          WHERE d.is_active = true
            AND (d.name ILIKE ${`%${search}%`} OR d.description ILIKE ${`%${search}%`} OR d.brand ILIKE ${`%${search}%`})
          ORDER BY d.created_at DESC
          LIMIT ${parseInt(limit)} OFFSET ${offset}
        `;

        countResult = await sql`
          SELECT COUNT(*) as total
          FROM drones d
          WHERE d.is_active = true
            AND (d.name ILIKE ${`%${search}%`} OR d.description ILIKE ${`%${search}%`} OR d.brand ILIKE ${`%${search}%`})
        `;
      } else {
        // Requête par défaut
        drones = await sql`
          SELECT
            d.id,
            d.name,
            d.slug,
            d.description,
            d.brand,
            d.model,
            d.price,
            d.images,
            d.specifications,
            d.features,
            d.stock_quantity,
            d.rating,
            d.review_count,
            d.weight,
            d.dimensions,
            d.battery_life,
            d.max_speed,
            d.max_range,
            d.camera_specs,
            d.created_at,
            c.id as category_id,
            c.name as category_name,
            c.slug as category_slug
          FROM drones d
          LEFT JOIN categories c ON d.category_id = c.id
          WHERE d.is_active = true
          ORDER BY d.created_at DESC
          LIMIT ${parseInt(limit)} OFFSET ${offset}
        `;

        countResult = await sql`
          SELECT COUNT(*) as total
          FROM drones d
          WHERE d.is_active = true
        `;
      }
    }
    const total = parseInt(countResult[0].total);

    // Formatage des données
    const formattedDrones = drones.map(drone => ({
      id: drone.id,
      name: drone.name,
      slug: drone.slug,
      description: drone.description,
      brand: drone.brand,
      model: drone.model,
      price: parseFloat(drone.price),
      images: drone.images || [],
      specifications: drone.specifications || {},
      features: drone.features || [],
      inStock: drone.stock_quantity > 0,
      stockQuantity: drone.stock_quantity,
      rating: parseFloat(drone.rating) || 0,
      reviewCount: drone.review_count || 0,
      weight: parseFloat(drone.weight) || null,
      dimensions: drone.dimensions || {},
      batteryLife: drone.battery_life,
      maxSpeed: drone.max_speed,
      maxRange: drone.max_range,
      cameraSpecs: drone.camera_specs || {},
      category: drone.category_id ? {
        id: drone.category_id,
        name: drone.category_name,
        slug: drone.category_slug
      } : null,
      createdAt: drone.created_at
    }));

    res.json({
      success: true,
      data: {
        drones: formattedDrones,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / parseInt(limit)),
          totalItems: total,
          itemsPerPage: parseInt(limit),
          hasNextPage: parseInt(page) < Math.ceil(total / parseInt(limit)),
          hasPrevPage: parseInt(page) > 1
        }
      }
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des drones:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des drones'
    });
  }
});

// @route   GET /api/drones/:id
// @desc    Obtenir un drone par ID
// @access  Public
router.get('/:id', validate(uuidSchema, 'params'), optionalAuth, async (req, res) => {
  try {
    const { id } = req.params;

    const drones = await sql`
      SELECT
        d.*,
        c.id as category_id,
        c.name as category_name,
        c.slug as category_slug
      FROM drones d
      LEFT JOIN categories c ON d.category_id = c.id
      WHERE d.id = ${id} AND d.is_active = true
    `;

    if (drones.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Drone non trouvé'
      });
    }

    const drone = drones[0];

    const formattedDrone = {
      id: drone.id,
      name: drone.name,
      slug: drone.slug,
      description: drone.description,
      brand: drone.brand,
      model: drone.model,
      price: parseFloat(drone.price),
      images: drone.images || [],
      specifications: drone.specifications || {},
      features: drone.features || [],
      inStock: drone.stock_quantity > 0,
      stockQuantity: drone.stock_quantity,
      rating: parseFloat(drone.rating) || 0,
      reviewCount: drone.review_count || 0,
      weight: parseFloat(drone.weight) || null,
      dimensions: drone.dimensions || {},
      batteryLife: drone.battery_life,
      maxSpeed: drone.max_speed,
      maxRange: drone.max_range,
      cameraSpecs: drone.camera_specs || {},
      category: drone.category_id ? {
        id: drone.category_id,
        name: drone.category_name,
        slug: drone.category_slug
      } : null,
      createdAt: drone.created_at,
      updatedAt: drone.updated_at
    };

    res.json({
      success: true,
      data: {
        drone: formattedDrone
      }
    });

  } catch (error) {
    console.error('Erreur lors de la récupération du drone:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération du drone'
    });
  }
});

module.exports = router;
