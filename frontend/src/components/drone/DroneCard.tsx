import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Star, ShoppingCart, Eye } from 'lucide-react';
import { Drone } from '../../types';
import { useAppDispatch } from '../../hooks/redux';
import { addToCart } from '../../store/slices/cartSlice';
import Button from '../common/Button';

interface DroneCardProps {
  drone: Drone;
}

const DroneCard: React.FC<DroneCardProps> = ({ drone }) => {
  const dispatch = useAppDispatch();

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    dispatch(addToCart({ drone, quantity: 1 }));
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`h-4 w-4 ${
          index < Math.floor(rating)
            ? 'text-yellow-400 fill-current'
            : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
      <Link to={`/drones/${drone.id}`}>
        <div className="relative">
          <img
            src={drone.images[0]}
            alt={drone.name}
            className="w-full h-48 object-cover"
          />
          {!drone.inStock && (
            <div className="absolute inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center">
              <span className="text-white font-semibold text-lg">Rupture de stock</span>
            </div>
          )}
          <div className="absolute top-2 right-2">
            <span className="bg-blue-600 text-white px-2 py-1 rounded-md text-sm font-medium">
              {drone.category.name}
            </span>
          </div>
        </div>
        
        <div className="p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-1 line-clamp-2">
            {drone.name}
          </h3>
          <p className="text-sm text-gray-600 mb-2">{drone.brand}</p>
          
          <div className="flex items-center mb-2">
            <div className="flex items-center">
              {renderStars(drone.rating)}
            </div>
            <span className="ml-2 text-sm text-gray-600">
              ({drone.reviewCount} avis)
            </span>
          </div>

          <p className="text-gray-700 text-sm mb-3 line-clamp-2">
            {drone.description}
          </p>

          <div className="flex items-center justify-between mb-3">
            <div className="text-sm text-gray-600">
              <span className="block">Autonomie: {drone.specifications.flightTime}min</span>
              <span className="block">Portée: {drone.specifications.range}km</span>
            </div>
            <div className="text-sm text-gray-600">
              <span className="block">Vitesse: {drone.specifications.maxSpeed}km/h</span>
              <span className="block">Poids: {drone.specifications.weight}kg</span>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-2xl font-bold text-blue-600">
              {drone.price.toLocaleString('fr-FR', {
                style: 'currency',
                currency: 'EUR',
              })}
            </span>
          </div>
        </div>
      </Link>
      
      <div className="px-4 pb-4 flex gap-2">
        <Button
          onClick={handleAddToCart}
          disabled={!drone.inStock}
          icon={ShoppingCart}
          size="sm"
          className="flex-1"
        >
          Ajouter au panier
        </Button>
        <Link to={`/drones/${drone.id}`}>
          <Button
            variant="outline"
            icon={Eye}
            size="sm"
          >
            Voir
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default DroneCard;