import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Shield, Truck, Headphones } from 'lucide-react';
import Button from '../components/common/Button';

const HomePage: React.FC = () => {
  return (
    <div>
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                Découvrez l'univers des 
                <span className="text-blue-200"> drones</span>
              </h1>
              <p className="text-xl mb-8 text-blue-100">
                De la photographie aérienne aux courses de vitesse, trouvez le drone parfait 
                pour vos besoins. Qualité professionnelle, prix compétitifs.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link to="/drones">
                  <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                    Explorer nos drones
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <Button variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-blue-600">
                  En savoir plus
                </Button>
              </div>
            </div>
            <div className="relative">
              <img
                src="https://via.placeholder.com/600x400"
                alt="Drone professionnel"
                className="rounded-lg shadow-2xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Pourquoi choisir DroneShop ?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Nous nous engageons à vous offrir la meilleure expérience d'achat 
              et les produits de la plus haute qualité.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Garantie qualité
              </h3>
              <p className="text-gray-600">
                Tous nos drones sont testés et garantis. Satisfaction garantie ou remboursé.
              </p>
            </div>
            
            <div className="text-center">
              <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Truck className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Livraison rapide
              </h3>
              <p className="text-gray-600">
                Livraison gratuite en 24-48h partout en France métropolitaine.
              </p>
            </div>
            
            <div className="text-center">
              <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Headphones className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Support expert
              </h3>
              <p className="text-gray-600">
                Notre équipe d'experts est là pour vous conseiller et vous accompagner.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Explorez nos catégories
            </h2>
            <p className="text-xl text-gray-600">
              Trouvez le drone parfait selon votre usage
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: 'Drones de loisir',
                description: 'Parfaits pour débuter et s\'amuser',
                image: 'https://via.placeholder.com/300x200',
                link: '/drones?category=loisir'
              },
              {
                title: 'Drones professionnels',
                description: 'Pour la photographie et la vidéographie',
                image: 'https://via.placeholder.com/300x200',
                link: '/drones?category=professionnel'
              },
              {
                title: 'Drones de course',
                description: 'Vitesse et agilité pour les compétitions',
                image: 'https://via.placeholder.com/300x200',
                link: '/drones?category=course'
              },
            ].map((category, index) => (
              <Link
                key={index}
                to={category.link}
                className="group bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300"
              >
                <img
                  src={category.image}
                  alt={category.title}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {category.title}
                  </h3>
                  <p className="text-gray-600">
                    {category.description}
                  </p>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-blue-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Prêt à prendre votre envol ?
          </h2>
          <p className="text-xl mb-8 text-blue-100">
            Découvrez notre sélection complète de drones et accessoires
          </p>
          <Link to="/drones">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
              Voir tous les drones
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
