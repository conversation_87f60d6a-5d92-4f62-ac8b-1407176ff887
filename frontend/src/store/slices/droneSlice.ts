import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Drone, PaginatedResponse } from '../../types';

interface DroneState {
  drones: Drone[];
  currentDrone: Drone | null;
  loading: boolean;
  error: string | null;
  total: number;
  page: number;
  totalPages: number;
  filters: {
    category?: string;
    priceRange?: [number, number];
    brand?: string;
    search?: string;
  };
}

const initialState: DroneState = {
  drones: [],
  currentDrone: null,
  loading: false,
  error: null,
  total: 0,
  page: 1,
  totalPages: 0,
  filters: {},
};

// Async thunks
export const fetchDrones = createAsyncThunk(
  'drones/fetchDrones',
  async (params: { page?: number; limit?: number; search?: string; category?: string }) => {
    // TODO: Replace with actual API call
    const mockDrones: Drone[] = [
      {
        id: '1',
        name: 'DJI Mini 3 Pro',
        brand: 'DJI',
        model: 'Mini 3 Pro',
        price: 899,
        description: 'Drone compact avec caméra 4K HDR',
        specifications: {
          maxSpeed: 57.6,
          flightTime: 34,
          maxPayload: 0.249,
          range: 12,
          battery: '2453 mAh',
          camera: '4K/60fps HDR',
          gps: true,
          weight: 0.249,
        },
        images: ['https://via.placeholder.com/400x300'],
        category: { id: '1', name: 'Loisir', description: 'Drones pour particuliers' },
        inStock: true,
        rating: 4.8,
        reviewCount: 127,
        features: ['4K HDR', 'OcuSync 3.0', 'ActiveTrack 4.0'],
      },
      {
        id: '2',
        name: 'DJI Air 2S',
        brand: 'DJI',
        model: 'Air 2S',
        price: 1199,
        description: 'Drone semi-professionnel avec capteur 1 pouce',
        specifications: {
          maxSpeed: 68.4,
          flightTime: 31,
          maxPayload: 0.595,
          range: 12,
          battery: '3500 mAh',
          camera: '5.4K/30fps',
          gps: true,
          weight: 0.595,
        },
        images: ['https://via.placeholder.com/400x300'],
        category: { id: '2', name: 'Semi-Pro', description: 'Drones semi-professionnels' },
        inStock: true,
        rating: 4.9,
        reviewCount: 89,
        features: ['Capteur 1"', 'MasterShots', 'FocusTrack'],
      },
    ];

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return {
      data: mockDrones,
      total: mockDrones.length,
      page: params.page || 1,
      limit: params.limit || 10,
      totalPages: Math.ceil(mockDrones.length / (params.limit || 10)),
    } as PaginatedResponse<Drone>;
  }
);

export const fetchDroneById = createAsyncThunk(
  'drones/fetchDroneById',
  async (id: string) => {
    // TODO: Replace with actual API call
    const mockDrone: Drone = {
      id,
      name: 'DJI Mini 3 Pro',
      brand: 'DJI',
      model: 'Mini 3 Pro',
      price: 899,
      description: 'Drone compact avec caméra 4K HDR',
      specifications: {
        maxSpeed: 57.6,
        flightTime: 34,
        maxPayload: 0.249,
        range: 12,
        battery: '2453 mAh',
        camera: '4K/60fps HDR',
        gps: true,
        weight: 0.249,
      },
      images: ['https://via.placeholder.com/400x300'],
      category: { id: '1', name: 'Loisir', description: 'Drones pour particuliers' },
      inStock: true,
      rating: 4.8,
      reviewCount: 127,
      features: ['4K HDR', 'OcuSync 3.0', 'ActiveTrack 4.0'],
    };

    await new Promise(resolve => setTimeout(resolve, 500));
    return mockDrone;
  }
);

const droneSlice = createSlice({
  name: 'drones',
  initialState,
  reducers: {
    setFilters: (state, action: PayloadAction<Partial<DroneState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {};
    },
    setPage: (state, action: PayloadAction<number>) => {
      state.page = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchDrones.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchDrones.fulfilled, (state, action) => {
        state.loading = false;
        state.drones = action.payload.data;
        state.total = action.payload.total;
        state.page = action.payload.page;
        state.totalPages = action.payload.totalPages;
      })
      .addCase(fetchDrones.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Erreur lors du chargement des drones';
      })
      .addCase(fetchDroneById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchDroneById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentDrone = action.payload;
      })
      .addCase(fetchDroneById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Erreur lors du chargement du drone';
      });
  },
});

export const { setFilters, clearFilters, setPage } = droneSlice.actions;
export default droneSlice.reducer;